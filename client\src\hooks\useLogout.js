import { toast } from "sonner";
import { useNavigate } from "react-router-dom";

export const useLogout = () => {
  const navigate = useNavigate();

  const logoutUser = async () => {
    try {
      // Optional: Call backend to invalidate session/token
      await fetch("http://localhost:3000/api/auth/logout", {
        method: "POST",
        credentials: "include", // important for cookies (if using session-based auth)
      });

      // Clear localStorage or tokens
      localStorage.removeItem("token"); // if you store token manually

      toast.success("Logged out successfully");
      navigate("/login");
    } catch (err) {
      console.error("Logout error:", err);
      toast.error("Failed to logout. Please try again.");
    }
  };

  return logoutUser;
};
